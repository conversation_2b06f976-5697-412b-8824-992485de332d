import sys
print(sys.path)
try:
    import engagement_app
    print("Package found at:", engagement_app.__file__)

    from engagement_app.api import EngagementDetector, QuickProcessor, quick_highlights
    print("EngagementDetector found at:", EngagementDetector.__file__)
    print("QuickProcessor found at:", QuickProcessor.__file__)
    print("quick_highlights found at:", quick_highlights.__file__)
except ImportError as e:
    print("Import error:", str(e))